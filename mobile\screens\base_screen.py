"""基础屏幕模块

这个模块提供了一个基础屏幕类,用于标准化屏幕初始化过程,
确保所有屏幕都遵循相同的初始化流程,并避免重复代码.
包含全局顶端导航栏的统一管理.
"""
from kivy.uix.screenmanager import Screen
from kivy.clock import Clock
from kivy.properties import BooleanProperty, StringProperty, ObjectProperty
from kivy.lang import Builder
from kivy.graphics import Color, Rectangle
import logging
from kivy.logger import Logger as KivyLogger
from kivy.metrics import dp
from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDIconButton
from kivymd.uix.card import MDCard
from kivymd.uix.scrollview import MDScrollView
from kivy.uix.image import Image
from kivy.uix.widget import Widget
from mobile.theme import AppTheme, AppMetrics, FontStyles, FontManager
import os

from mobile.widgets.logo_manager import get_logo_manager
from mobile.widgets.top_app_bar import GlobalTopAppBar, create_top_app_bar, create_simple_top_app_bar
from mobile.widgets.logo import HealthLogo

# NavBarButton KV定义
NAV_BAR_BUTTON_KV = '''
<NavBarButton>:
    orientation: 'vertical'
    size_hint_y: None
    height: dp(48)
    spacing: dp(1)
    padding: [dp(4), dp(2), dp(4), dp(2)]

    MDIconButton:
        id: icon_button
        icon: root.icon
        theme_icon_color: "Custom"
        icon_color: root.icon_color
        icon_size: dp(20)
        size_hint: None, None
        size: dp(24), dp(24)
        pos_hint: {'center_x': 0.5}
        on_release: root.on_press()

    MDLabel:
        id: label
        text: root.text
        theme_text_color: "Custom"
        text_color: root.text_color
        font_size: dp(10)
        halign: 'center'
        size_hint_y: None
        height: dp(16)
        text_size: self.width, None
'''

# 定义BaseScreen的KV模板
BASE_SCREEN_KV = '''
<BaseScreen>:
    # 为避免与子类KV重复定义根容器，BaseScreen不再在KV中添加任何子部件
'''


class NavBarButton(MDBoxLayout):
    """底部导航栏按钮组件"""
    icon = StringProperty("home")
    text = StringProperty("首页")
    is_active = BooleanProperty(False)
    callback = ObjectProperty(None)
    icon_color = ObjectProperty([0.6, 0.6, 0.6, 1.0])
    text_color = ObjectProperty([0.6, 0.6, 0.6, 1.0])

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.bind(is_active=self.update_colors)
        Clock.schedule_once(self.update_colors, 0.1)

    def update_colors(self, *args):
        """更新按钮颜色状态"""
        try:
            app = MDApp.get_running_app()
            if app is not None and self.is_active:
                self.icon_color = app.theme.PRIMARY if hasattr(app, 'theme') else [0.2, 0.6, 1.0, 1.0]
                self.text_color = app.theme.PRIMARY if hasattr(app, 'theme') else [0.2, 0.6, 1.0, 1.0]
            else:
                self.icon_color = [0.6, 0.6, 0.6, 1.0]
                self.text_color = [0.6, 0.6, 0.6, 1.0]
        except Exception as e:
            # 使用默认颜色
            if self.is_active:
                self.icon_color = [0.2, 0.6, 1.0, 1.0]
                self.text_color = [0.2, 0.6, 1.0, 1.0]
            else:
                self.icon_color = [0.6, 0.6, 0.6, 1.0]
                self.text_color = [0.6, 0.6, 0.6, 1.0]

    def on_press(self):
        """按钮点击事件"""
        if self.callback:
            self.callback()

# 加载KV模板
try:
    Builder.load_string(NAV_BAR_BUTTON_KV)
    Builder.load_string(BASE_SCREEN_KV)
except Exception as e:
    KivyLogger.error(f"加载KV模板失败: {e}")

class BaseScreen(Screen):
    """基础屏幕类
    这个类提供了标准化的屏幕初始化流程,包括:
    1. 延迟初始化UI,确保KV规则已应用
    2. 自动清理重复的Logo
    3. 提供通用的生命周期方法
    4. 统一的顶端导航栏管理
    """
    is_initialized = BooleanProperty(False)

    # 顶端导航栏相关属性
    screen_title = StringProperty("标题")
    show_top_bar = BooleanProperty(True)  # 是否显示顶端导航栏
    top_bar_action_icon = StringProperty("refresh")  # 右侧操作按钮图标
    top_app_bar = ObjectProperty(None)  # 顶端导航栏组件引用

    def __init__(self, **kwargs):
        """初始化基础屏幕"""
        super().__init__(**kwargs)
        self._init_scheduled = False

        # 初始化logger
        self.logger = logging.getLogger('BaseScreen')

        # 设置背景颜色
        self.setup_background()

        # 在KV规则应用后延迟初始化UI
        # Clock.schedule_once(self._delayed_init, 0.1)

    def _delayed_init(self, dt):
        """延迟初始化,确保KV规则已应用"""
        if not self._init_scheduled:
            self._init_scheduled = True
            # 不再在这里调度init_ui,让on_enter方法处理

    def init_ui(self, dt=0):
        """初始化UI - 简化版本

        子类应该覆盖这个方法来实现自己的UI初始化逻辑.
        """
        # 检查是否已经初始化,避免重复初始化
        if self.is_initialized:
            # 即使已初始化，也更新导航状态
            if not self.__class__.__name__.lower().startswith('login'):
                self.update_navigation_state()
            return True

        # 1) 确保main_layout存在
        self.ensure_main_layout()

        # 2) 设置顶端导航栏（如需要）
        if self.show_top_bar and not getattr(self, 'top_app_bar', None):
            self.setup_top_app_bar()

        # 3) 添加Logo（若不存在）
        self.setup_logo()

        # 4) 清理重复的Logo
        self.cleanup_duplicate_logos()

        # 5) 初始化底部导航栏（除login_screen外）
        if not self.__class__.__name__.lower().startswith('login'):
            self.setup_bottom_navigation()

        self.is_initialized = True
        return True

    def ensure_main_layout(self):
        """
        确保主布局容器存在，简化版本避免复杂的布局重构
        """
        from kivymd.uix.boxlayout import MDBoxLayout
        try:
            # 确保 ids 字典存在
            if not hasattr(self, 'ids') or not isinstance(self.ids, dict):
                self.ids = {}

            # 查找或创建主布局容器
            main_layout = self.ids.get('main_layout')
            if main_layout is None:
                # 查找现有的垂直布局容器
                for child in self.children:
                    if (hasattr(child, 'orientation') and
                        child.orientation == 'vertical'):
                        main_layout = child
                        break

                # 如果没有找到，创建新的主布局
                if main_layout is None:
                    main_layout = MDBoxLayout(
                        orientation='vertical',
                        spacing=0,
                        padding=[0, 0, 0, 0]
                    )
                    self.add_widget(main_layout)

                # 注册到 ids
                self.ids['main_layout'] = main_layout

            # 确保底部导航容器存在
            nav_container = self.ids.get('nav_bar_container')
            if nav_container is None:
                nav_container = MDBoxLayout(
                    orientation='horizontal',
                    size_hint_y=None,
                    height=dp(56),
                    spacing=0
                )
                nav_container.id = 'nav_bar_container'
                main_layout.add_widget(nav_container)
                self.ids['nav_bar_container'] = nav_container

            return main_layout

        except Exception as e:
            self.logger.error(f"[BaseScreen] ensure_main_layout 失败: {e}")
            # 创建最简单的布局结构
            main_layout = MDBoxLayout(orientation='vertical', spacing=0)
            self.add_widget(main_layout)
            if not hasattr(self, 'ids'):
                self.ids = {}
            self.ids['main_layout'] = main_layout
            return main_layout

    def _count_logos_recursive(self, widget) -> int:
        """递归统计给定widget子树中的HealthLogo数量"""
        count = 0
        try:
            if isinstance(widget, HealthLogo) or widget.__class__.__name__ == 'HealthLogo':
                count += 1
        except Exception:
            pass
        if hasattr(widget, 'children'):
            for child in getattr(widget, 'children', []):
                count += self._count_logos_recursive(child)
        return count

    def _audit_runtime(self):
        """运行时自检 - 简化版本"""
        try:
            # 统计 Logo 数
            logo_count = self._count_logos_recursive(self)
            if logo_count == 0:
                self.logger.debug(f"[BaseScreen] {self.__class__.__name__}: 未发现Logo")
            elif logo_count > 1:
                self.logger.warning(f"[BaseScreen] {self.__class__.__name__}: 检测到 {logo_count} 个Logo，将由LogoManager清理")

        except Exception as e:
            self.logger.error(f"[BaseScreen] 运行时自检出错: {e}")
    def _cleanup_duplicate_nav_bars(self, only_check: bool = False) -> int:
        """清理重复的底部导航容器

        - 统计并可选地移除多余的 nav_bar_container 容器，确保每屏最多一个
        - 返回检测到的容器数量
        """
        try:
            from kivymd.uix.boxlayout import MDBoxLayout
            target_list = []

            def walk(widget):
                try:
                    # 只关心布局容器
                    if isinstance(widget, MDBoxLayout):
                        wid = getattr(widget, 'id', '')
                        if wid == 'nav_bar_container':
                            target_list.append(widget)
                        else:
                            # 兜底：判断其子项是否全部为 NavBarButton（疑似重复容器）
                            try:
                                from screens.base_screen import NavBarButton
                                if hasattr(widget, 'children') and widget.children:
                                    nav_like = all(isinstance(c, NavBarButton) for c in widget.children)
                                    if nav_like:
                                        target_list.append(widget)
                            except Exception:
                                pass
                    # 递归遍历
                    if hasattr(widget, 'children'):
                        for ch in widget.children:
                            walk(ch)
                except Exception:
                    pass

            walk(self)
            count = len(target_list)
            if only_check:
                return count

            # 仅保留 ids['nav_bar_container'] 指向的那一个，其余移除
            keep = None
            try:
                keep = self.ids.get('nav_bar_container') if hasattr(self, 'ids') else None
            except Exception:
                keep = None

            for w in target_list:
                if keep is not None and w is keep:
                    continue
                try:
                    if getattr(w, 'parent', None):
                        w.parent.remove_widget(w)
                except Exception:
                    pass

            return count
        except Exception:
            return 0


    def on_enter(self, *args):
        """进入屏幕时调用

        如果屏幕尚未初始化,会触发初始化过程.
        子类应该调用super().on_enter()以确保正确的初始化流程.
        """
        # 如果尚未初始化,触发初始化过程
        if not self.is_initialized:
            self.init_ui()
        else:
            # 如果已初始化，更新导航状态
            if not self.__class__.__name__.lower().startswith('login'):
                self.update_navigation_state()


    def on_leave(self, *args):
        """离开屏幕时调用

        子类可以覆盖这个方法来实现自己的清理逻辑.
        """
        pass

    def on_back(self):
        """处理返回按钮点击事件

        默认行为是返回到上一个屏幕,如果没有历史记录则返回主页.
        子类可以覆盖这个方法来实现自定义的返回逻辑.
        """
        app = self.get_app()
        if app and app.root:
            # 使用屏幕管理器的导航历史功能
            if hasattr(app.root, 'go_back'):
                success = app.root.go_back()
                if not success:
                    # 如果go_back返回False(没有历史记录),已经自动返回主页
                    pass
            else:
                # 兼容性处理:如果屏幕管理器没有go_back方法,直接返回主页
                app.root.current = 'homepage_screen'

    def go_back(self):
        """返回上一个屏幕

        这是on_back的别名方法,用于兼容性.
        """
        self.on_back()

    def setup_top_app_bar(self):
        """设置顶端导航栏"""
        if not self.show_top_bar:
            return

        # 已存在直接返回
        if getattr(self, 'top_app_bar', None):
            return

        # 获取主布局容器
        main_layout = self.ensure_main_layout()
        if not main_layout:
            self.logger.error(f"[BaseScreen] 无法获取main_layout,跳过顶端导航栏设置")
            return

        try:
            # 创建顶端导航栏
            self.top_app_bar = GlobalTopAppBar(
                title=self.screen_title,
                action_icon=self.top_bar_action_icon,
                parent_screen=self
            )

            # 将顶栏添加到主布局的最顶部
            if self.top_app_bar.parent:
                self.top_app_bar.parent.remove_widget(self.top_app_bar)
            main_layout.add_widget(self.top_app_bar, index=len(main_layout.children))

        except Exception as e:
            self.logger.error(f"[BaseScreen] 创建顶端导航栏失败: {e}")
            import traceback
            traceback.print_exc()

    def setup_logo(self):
        """设置Logo - 简化版本"""
        main_layout = self.ensure_main_layout()
        if not main_layout:
            self.logger.warning(f"BaseScreen: 无法获取main_layout, 跳过Logo设置")
            return

        # 检查是否已经有Logo
        def has_logo_recursive(widget):
            try:
                if isinstance(widget, HealthLogo) or widget.__class__.__name__ == 'HealthLogo':
                    return True
            except Exception:
                pass
            if hasattr(widget, 'children'):
                for child in getattr(widget, 'children', []):
                    if has_logo_recursive(child):
                        return True
            return False

        if has_logo_recursive(self):
            self.logger.debug(f"BaseScreen: 屏幕中已存在Logo，跳过添加")
            return

        # 添加屏幕级别的Logo标记，防止重复创建
        if hasattr(self, '_logo_added') and self._logo_added:
            self.logger.debug(f"BaseScreen: 已标记为添加过Logo，跳过重复添加")
            return

        try:
            self.logger.info(f"BaseScreen: 为 {self.__class__.__name__} 添加Logo")
            logo = HealthLogo()
            logo.size_hint_y = None
            logo.height = dp(120)

            # 计算插入位置：如果有顶栏，Logo在顶栏之后；否则在最顶部
            insert_index = 0
            if hasattr(self, 'top_app_bar') and self.top_app_bar and self.top_app_bar.parent == main_layout:
                # 找到顶栏的位置，Logo插入到其后
                for i, child in enumerate(main_layout.children):
                    if child == self.top_app_bar:
                        insert_index = i
                        break

            if logo.parent:
                logo.parent.remove_widget(logo)
            main_layout.add_widget(logo, index=insert_index)

            # 标记已添加Logo
            self._logo_added = True

        except Exception as e:
            self.logger.error(f"BaseScreen: 添加Logo失败: {e}")
            import traceback
            traceback.print_exc()

    def on_action(self):
        """处理顶端导航栏右侧操作按钮点击事件

        子类可以覆盖这个方法来实现自定义的操作逻辑.
        默认行为是刷新数据.
        """
        if hasattr(self, 'refresh_data'):
            self.refresh_data()

    def cleanup_duplicate_logos(self):
        """清理重复的Logo"""
        try:
            from mobile.widgets.logo_manager import get_logo_manager
            logo_manager = get_logo_manager()
            removed_count = logo_manager.cleanup_duplicate_logos()
            if removed_count > 0:
                self.logger.info(f"[BaseScreen] 清理了 {removed_count} 个重复的Logo")
        except Exception as e:
            self.logger.error(f"[BaseScreen] 清理重复Logo时出错: {e}")

    def refresh_data(self):
        """刷新数据

        子类可以覆盖这个方法来实现自定义的数据刷新逻辑.
        """
        pass

    def set_screen_title(self, title):
        """设置屏幕标题

        Args:
            title (str): 新的标题文本
        """
        self.screen_title = title
        if self.top_app_bar:
            self.top_app_bar.set_title(title)

    def set_action_icon(self, icon):
        """设置顶端导航栏右侧操作按钮图标

        Args:
            icon (str): 图标名称,空字符串表示隐藏按钮
        """
        self.top_bar_action_icon = icon
        if self.top_app_bar:
            self.top_app_bar.set_action_icon(icon)

    def hide_top_bar(self):
        """隐藏顶端导航栏"""
        self.show_top_bar = False
        if self.top_app_bar and self.top_app_bar.parent:
            self.top_app_bar.parent.remove_widget(self.top_app_bar)

    def show_top_bar_widget(self):
        """显示顶端导航栏"""
        self.show_top_bar = True
        if not self.top_app_bar:
            self.setup_top_app_bar()

    def setup_background(self):
        """设置屏幕背景颜色,与homepage保持一致"""
        try:
            app = self.get_app()
            # 使用与homepage相同的背景色
            bg_color = app.theme.PRIMARY_LIGHT if app and hasattr(app, 'theme') and hasattr(app.theme, 'PRIMARY_LIGHT') else [0.9, 0.95, 1.0, 1.0]

            if self.canvas and hasattr(self.canvas, 'before'):
                with self.canvas.before:
                    Color(*bg_color)
                    self.rect = Rectangle(size=self.size, pos=self.pos)

                # 绑定尺寸变化事件
                # 修复:所有Widget实例都有bind方法,不需要检查
                self.bind(size=self._update_rect, pos=self._update_rect)  # type: ignore

        except Exception as e:
            self.logger.error(f"BaseScreen: 设置背景颜色失败: {e}")
            # 使用默认浅蓝色背景
            if self.canvas and hasattr(self.canvas, 'before'):
                with self.canvas.before:
                    Color(0.9, 0.95, 1.0, 1.0)
                    self.rect = Rectangle(size=self.size, pos=self.pos)
                # 修复:所有Widget实例都有bind方法,不需要检查
                self.bind(size=self._update_rect, pos=self._update_rect)  # type: ignore

    def _update_rect(self, instance, value):
        """更新背景矩形尺寸"""
        if hasattr(self, 'rect'):
            self.rect.pos = instance.pos
            self.rect.size = instance.size

    def get_app(self):
        """获取应用实例

        Returns:
            MDApp: 应用实例
        """
        return MDApp.get_running_app()

    @staticmethod
    def load_kv_string(kv_string):
        """加载KV语言字符串

        这个方法提供了一个安全的方式来加载KV语言字符串,
        避免重复加载导致的错误.

        Args:
            kv_string: KV语言字符串

        Returns:
            bool: 如果成功加载,返回True;否则返回False
        """
        try:
            Builder.load_string(kv_string)
            return True
        except Exception as e:
            print(f"加载KV字符串失败: {e}")
            return False

    def setup_bottom_navigation(self):
        """设置底部导航栏

        为除login_screen外的所有页面添加底部导航栏
        """
        try:
            # 先清理可能存在的重复底部容器
            try:
                self._cleanup_duplicate_nav_bars()
            except Exception:
                pass

            nav_container = self.ids.get('nav_bar_container')
            if not nav_container:
                self.logger.warning("[BaseScreen] nav_bar_container未找到")
                return

            # 记录调试信息
            init_flag = getattr(self, '_bottom_nav_initialized', False)
            self.logger.debug(f"[BaseScreen] 准备设置底部导航: screen={self.__class__.__name__}, 已初始化标志={init_flag}, 现有子项数={len(nav_container.children)}")

            # 检查是否已经添加过导航按钮，避免重复添加
            if init_flag:
                # 如果已经初始化，只更新激活状态
                self.update_navigation_state()
                return

            # 如果容器已有子项，优先清空（避免误判造成的残留）
            if len(nav_container.children) > 0:
                nav_container.clear_widgets()

            # 导航按钮配置
            nav_buttons = [
                {'icon': 'home', 'text': '首页', 'screen': 'homepage_screen'},
                {'icon': 'heart-pulse', 'text': '健康数据', 'screen': 'health_data_management'},
                {'icon': 'shield-alert', 'text': '风险管理', 'screen': 'health_risk_management'},
                {'icon': 'account', 'text': '我的', 'screen': 'profile_page'}
            ]

            # 创建导航按钮
            for button_config in nav_buttons:
                nav_button = NavBarButton(
                    icon=button_config['icon'],
                    text=button_config['text'],
                    callback=lambda screen=button_config['screen']: self.navigate_to_screen(screen)
                )

                # 设置当前页面按钮为激活状态
                current_screen = self.__class__.__name__.lower().replace('screen', '')
                target_screen = button_config['screen'].replace('_screen', '')
                nav_button.is_active = (current_screen == target_screen or
                                      current_screen == target_screen + '_screen')

                nav_container.add_widget(nav_button)

            # 标记底部导航已初始化
            self._bottom_nav_initialized = True
            self.logger.debug(f"[BaseScreen] 底部导航初始化完成: screen={self.__class__.__name__}, 按钮数={len(nav_container.children)}")

        except Exception as e:
            self.logger.error(f"[BaseScreen] 设置底部导航栏失败: {e}")

    def navigate_to_screen(self, screen_name):
        """导航到指定页面

        Args:
            screen_name (str): 目标页面名称
        """
        try:
            app = self.get_app()
            if app and hasattr(app, 'root'):
                screen_manager = app.root
                if hasattr(screen_manager, 'current'):
                    screen_manager.current = screen_name
                    self.logger.info(f"[BaseScreen] 导航到页面: {screen_name}")
                else:
                    self.logger.warning(f"[BaseScreen] ScreenManager未找到")
            else:
                self.logger.warning(f"[BaseScreen] 应用实例未找到")
        except Exception as e:
            self.logger.error(f"[BaseScreen] 导航失败: {e}")

    def update_navigation_state(self):
        """更新导航栏状态

        根据当前页面更新导航按钮的激活状态
        """
        try:
            nav_container = self.ids.get('nav_bar_container')
            if not nav_container:
                return

            current_screen = self.__class__.__name__.lower().replace('screen', '')

            # 更新所有导航按钮状态
            for child in nav_container.children:
                if isinstance(child, NavBarButton):
                    # 根据按钮文本判断对应的页面
                    button_screen_map = {
                        '首页': 'homepage',
                        '健康数据': 'health_data_management',
                        '风险管理': 'health_risk_management',
                        '我的': 'profile_page'
                    }

                    button_screen = button_screen_map.get(child.text, '')
                    # 修复激活状态判断逻辑
                    child.is_active = (current_screen == button_screen or
                                     current_screen == button_screen + '_screen')
                    child.update_colors()

        except Exception as e:
            self.logger.error(f"[BaseScreen] 更新导航状态失败: {e}")